#!/usr/bin/env python3
"""
Debug script to inspect functional activations and understand why steering shows no changes.
"""

import torch
import sys
import os

# Add the project root to the path
sys.path.append('/data_x/junkim100/projects/scheming_sae')

def inspect_functional_activations(activation_file_path):
    """Inspect the functional activations file to understand the steering vectors."""
    print(f"Loading functional activations from: {activation_file_path}")
    
    try:
        data = torch.load(activation_file_path, map_location='cpu')
        print(f"Keys in activation file: {list(data.keys())}")
        
        if "z_scheming" in data and "z_truthful" in data:
            z_scheming = data["z_scheming"]
            z_truthful = data["z_truthful"]
            
            print(f"\nz_scheming shape: {z_scheming.shape}")
            print(f"z_scheming dtype: {z_scheming.dtype}")
            print(f"z_scheming norm: {z_scheming.norm().item():.6f}")
            print(f"z_scheming max: {z_scheming.max().item():.6f}")
            print(f"z_scheming min: {z_scheming.min().item():.6f}")
            print(f"z_scheming non-zero elements: {(z_scheming != 0).sum().item()}")
            
            print(f"\nz_truthful shape: {z_truthful.shape}")
            print(f"z_truthful dtype: {z_truthful.dtype}")
            print(f"z_truthful norm: {z_truthful.norm().item():.6f}")
            print(f"z_truthful max: {z_truthful.max().item():.6f}")
            print(f"z_truthful min: {z_truthful.min().item():.6f}")
            print(f"z_truthful non-zero elements: {(z_truthful != 0).sum().item()}")
            
            # Check if vectors are essentially zero
            if z_scheming.norm().item() < 1e-6:
                print("\n⚠️  WARNING: z_scheming vector is essentially zero!")
            if z_truthful.norm().item() < 1e-6:
                print("\n⚠️  WARNING: z_truthful vector is essentially zero!")
                
            # Show some non-zero values
            scheming_nonzero = z_scheming[z_scheming != 0]
            truthful_nonzero = z_truthful[z_truthful != 0]
            
            if len(scheming_nonzero) > 0:
                print(f"\nSample z_scheming non-zero values: {scheming_nonzero[:10]}")
            if len(truthful_nonzero) > 0:
                print(f"Sample z_truthful non-zero values: {truthful_nonzero[:10]}")
                
        if "functional_metadata" in data:
            metadata = data["functional_metadata"]
            print(f"\nFunctional metadata: {metadata}")
            
        if "mi_result" in data:
            mi_result = data["mi_result"]
            print(f"\nMI result keys: {list(mi_result.keys()) if isinstance(mi_result, dict) else 'Not a dict'}")
            if isinstance(mi_result, dict):
                if "expectation" in mi_result:
                    expectation = mi_result["expectation"]
                    print(f"Expectation shape: {expectation.shape}")
                    print(f"Expectation norm: {expectation.norm().item():.6f}")
                    print(f"Expectation max: {expectation.max().item():.6f}")
                    print(f"Expectation min: {expectation.min().item():.6f}")
                    
                if "scheming_features" in mi_result:
                    scheming_features = mi_result["scheming_features"]
                    print(f"Number of scheming features: {len(scheming_features)}")
                    
                if "truthful_features" in mi_result:
                    truthful_features = mi_result["truthful_features"]
                    print(f"Number of truthful features: {len(truthful_features)}")
                    
    except Exception as e:
        print(f"Error loading activation file: {e}")
        return False
        
    return True

if __name__ == "__main__":
    # Inspect layer 0 activations
    activation_file = "/data_x/junkim100/projects/scheming_sae/itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/activations/functional_activations_Llama_3.1_8B_Instruct_L0_alpha1.0_topk0.15_Llama3_1_8B_Base_LXM_32x_20250704_050251.pt"
    
    if os.path.exists(activation_file):
        inspect_functional_activations(activation_file)
    else:
        print(f"Activation file not found: {activation_file}")
